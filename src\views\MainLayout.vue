<template>
  <div class="main-layout">
    <!-- 顶部导航栏 -->
    <header class="header">
      <div class="header-content">
        <div class="logo-section">
          <img src="/favicon.ico" alt="Logo" class="logo">
          <h1 class="system-title">GPU管理系统</h1>
        </div>
        
        <div class="user-section">
          <div class="user-info">
            <span class="welcome-text">欢迎，{{ username }}</span>
            <div class="user-avatar">
              <span>{{ username.charAt(0).toUpperCase() }}</span>
            </div>
          </div>
          <button @click="handleLogout" class="logout-btn">
            退出登录
          </button>
        </div>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <div class="dashboard-container">
        <div class="welcome-card">
          <h2>欢迎使用GPU管理系统</h2>
          <p>这里是系统主页，您可以在这里管理和监控GPU资源。</p>
        </div>

        <!-- 功能卡片网格 -->
        <div class="feature-grid">
          <div class="feature-card">
            <div class="card-icon">🖥️</div>
            <h3>GPU监控</h3>
            <p>实时监控GPU使用情况和性能指标</p>
            <button class="card-btn">查看详情</button>
          </div>

          <div class="feature-card">
            <div class="card-icon">📊</div>
            <h3>资源统计</h3>
            <p>查看GPU资源使用统计和历史数据</p>
            <button class="card-btn">查看统计</button>
          </div>

          <div class="feature-card">
            <div class="card-icon">⚙️</div>
            <h3>系统设置</h3>
            <p>配置系统参数和用户权限管理</p>
            <button class="card-btn">进入设置</button>
          </div>

          <div class="feature-card">
            <div class="card-icon">📝</div>
            <h3>日志管理</h3>
            <p>查看系统运行日志和操作记录</p>
            <button class="card-btn">查看日志</button>
          </div>
        </div>

        <!-- 快速状态面板 -->
        <div class="status-panel">
          <h3>系统状态</h3>
          <div class="status-grid">
            <div class="status-item">
              <div class="status-label">在线GPU</div>
              <div class="status-value">{{ systemStatus.onlineGPUs }}</div>
            </div>
            <div class="status-item">
              <div class="status-label">总GPU数</div>
              <div class="status-value">{{ systemStatus.totalGPUs }}</div>
            </div>
            <div class="status-item">
              <div class="status-label">CPU使用率</div>
              <div class="status-value">{{ systemStatus.cpuUsage }}%</div>
            </div>
            <div class="status-item">
              <div class="status-label">内存使用率</div>
              <div class="status-value">{{ systemStatus.memoryUsage }}%</div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'MainLayout',
  setup() {
    const router = useRouter()
    
    // 用户信息
    const username = ref('管理员')
    
    // 系统状态数据
    const systemStatus = reactive({
      onlineGPUs: 8,
      totalGPUs: 10,
      cpuUsage: 45,
      memoryUsage: 67
    })

    // 处理退出登录
    const handleLogout = () => {
      if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('token')
        localStorage.removeItem('rememberedUsername')
        router.push('/login')
      }
    }

    // 模拟获取系统状态数据
    const fetchSystemStatus = () => {
      // 这里可以调用API获取真实数据
      console.log('获取系统状态数据')
    }

    onMounted(() => {
      fetchSystemStatus()
    })

    return {
      username,
      systemStatus,
      handleLogout
    }
  }
}
</script>

<style scoped>
.main-layout {
  min-height: 100vh;
  background: #f5f7fa;
}

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logo {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.system-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.user-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.welcome-text {
  font-size: 14px;
  opacity: 0.9;
}

.user-avatar {
  width: 35px;
  height: 35px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
}

.logout-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 主要内容区域 */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 30px 20px;
}

.dashboard-container {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* 欢迎卡片 */
.welcome-card {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  text-align: center;
}

.welcome-card h2 {
  color: #333;
  margin: 0 0 10px 0;
  font-size: 28px;
  font-weight: 600;
}

.welcome-card p {
  color: #666;
  margin: 0;
  font-size: 16px;
}

/* 功能卡片网格 */
.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.feature-card {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.card-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.feature-card h3 {
  color: #333;
  margin: 0 0 10px 0;
  font-size: 20px;
  font-weight: 600;
}

.feature-card p {
  color: #666;
  margin: 0 0 20px 0;
  font-size: 14px;
  line-height: 1.5;
}

.card-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
}

.card-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

/* 状态面板 */
.status-panel {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.status-panel h3 {
  color: #333;
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 600;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.status-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #e9ecef;
}

.status-label {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
}

.status-value {
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .user-section {
    flex-direction: column;
    gap: 10px;
  }
  
  .main-content {
    padding: 20px 15px;
  }
  
  .feature-grid {
    grid-template-columns: 1fr;
  }
  
  .status-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .system-title {
    font-size: 20px;
  }
  
  .welcome-card h2 {
    font-size: 24px;
  }
}
</style>
