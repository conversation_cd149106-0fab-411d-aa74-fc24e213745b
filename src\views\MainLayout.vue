<template>
  <div class="admin-layout">
    <!-- 侧边栏 -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <div class="logo">
          <img src="/favicon.ico" alt="Logo" class="logo-img">
          <span class="logo-text">GPU管理后台</span>
        </div>
      </div>

      <nav class="sidebar-nav">
        <ul class="nav-menu">
          <li class="nav-item active">
            <a href="#" class="nav-link">
              <i class="nav-icon">📊</i>
              <span>仪表盘</span>
            </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link">
              <i class="nav-icon">🖥️</i>
              <span>GPU监控</span>
            </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link">
              <i class="nav-icon">📈</i>
              <span>性能分析</span>
            </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link">
              <i class="nav-icon">⚙️</i>
              <span>系统设置</span>
            </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link">
              <i class="nav-icon">👥</i>
              <span>用户管理</span>
            </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link">
              <i class="nav-icon">📝</i>
              <span>日志管理</span>
            </a>
          </li>
        </ul>
      </nav>
    </aside>

    <!-- 主要内容区域 -->
    <div class="main-wrapper">
      <!-- 顶部导航栏 -->
      <header class="top-header">
        <div class="header-left">
          <button class="menu-toggle" @click="toggleSidebar">
            <span class="hamburger"></span>
          </button>
          <div class="breadcrumb">
            <span>首页</span>
            <span class="separator">/</span>
            <span>仪表盘</span>
          </div>
        </div>

        <div class="header-right">
          <div class="header-actions">
            <button class="action-btn notification-btn">
              <i class="icon">🔔</i>
              <span class="badge">3</span>
            </button>
            <button class="action-btn message-btn">
              <i class="icon">💬</i>
            </button>
          </div>

          <div class="user-dropdown">
            <div class="user-info" @click="toggleUserMenu">
              <div class="user-avatar">
                <img src="/favicon.ico" alt="Avatar" class="avatar-img">
              </div>
              <div class="user-details">
                <span class="user-name">{{ username }}</span>
                <span class="user-role">管理员</span>
              </div>
              <i class="dropdown-arrow">▼</i>
            </div>

            <div class="dropdown-menu" v-show="showUserMenu">
              <a href="#" class="dropdown-item">
                <i class="item-icon">👤</i>
                个人资料
              </a>
              <a href="#" class="dropdown-item">
                <i class="item-icon">⚙️</i>
                账户设置
              </a>
              <div class="dropdown-divider"></div>
              <a href="#" class="dropdown-item" @click="handleLogout">
                <i class="item-icon">🚪</i>
                退出登录
              </a>
            </div>
          </div>
        </div>
      </header>

      <!-- 主要内容 -->
      <main class="main-content">
        <div class="dashboard-container">
          <!-- 统计卡片 -->
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-icon gpu-icon">🖥️</div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.totalGPUs }}</div>
                <div class="stat-label">总GPU数量</div>
                <div class="stat-change positive">+2.5%</div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon active-icon">⚡</div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.activeGPUs }}</div>
                <div class="stat-label">活跃GPU</div>
                <div class="stat-change positive">+5.2%</div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon usage-icon">📊</div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.avgUsage }}%</div>
                <div class="stat-label">平均使用率</div>
                <div class="stat-change negative">-1.8%</div>
              </div>
            </div>

            <div class="stat-card">
              <div class="stat-icon temp-icon">🌡️</div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.avgTemp }}°C</div>
                <div class="stat-label">平均温度</div>
                <div class="stat-change neutral">0.0%</div>
              </div>
            </div>
          </div>

          <!-- 图表和表格区域 -->
          <div class="content-grid">
            <!-- GPU使用率图表 -->
            <div class="chart-card">
              <div class="card-header">
                <h3 class="card-title">GPU使用率趋势</h3>
                <div class="card-actions">
                  <select class="time-selector">
                    <option>最近24小时</option>
                    <option>最近7天</option>
                    <option>最近30天</option>
                  </select>
                </div>
              </div>
              <div class="chart-container">
                <div class="chart-placeholder">
                  <div class="chart-line"></div>
                  <div class="chart-data">
                    <div class="data-point" style="left: 10%; height: 60%"></div>
                    <div class="data-point" style="left: 20%; height: 75%"></div>
                    <div class="data-point" style="left: 30%; height: 45%"></div>
                    <div class="data-point" style="left: 40%; height: 80%"></div>
                    <div class="data-point" style="left: 50%; height: 65%"></div>
                    <div class="data-point" style="left: 60%; height: 90%"></div>
                    <div class="data-point" style="left: 70%; height: 55%"></div>
                    <div class="data-point" style="left: 80%; height: 70%"></div>
                    <div class="data-point" style="left: 90%; height: 85%"></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- GPU列表 -->
            <div class="table-card">
              <div class="card-header">
                <h3 class="card-title">GPU设备列表</h3>
                <button class="refresh-btn">🔄 刷新</button>
              </div>
              <div class="table-container">
                <table class="data-table">
                  <thead>
                    <tr>
                      <th>设备ID</th>
                      <th>型号</th>
                      <th>使用率</th>
                      <th>温度</th>
                      <th>内存</th>
                      <th>状态</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="gpu in gpuList" :key="gpu.id">
                      <td>{{ gpu.id }}</td>
                      <td>{{ gpu.model }}</td>
                      <td>
                        <div class="usage-bar">
                          <div class="usage-fill" :style="{ width: gpu.usage + '%' }"></div>
                          <span class="usage-text">{{ gpu.usage }}%</span>
                        </div>
                      </td>
                      <td>{{ gpu.temperature }}°C</td>
                      <td>{{ gpu.memory }}</td>
                      <td>
                        <span class="status-badge" :class="gpu.status">
                          {{ gpu.statusText }}
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <!-- 快速操作面板 -->
          <div class="quick-actions">
            <div class="action-card">
              <div class="action-icon">⚡</div>
              <div class="action-content">
                <h4>性能优化</h4>
                <p>优化GPU性能配置</p>
              </div>
              <button class="action-btn-small">执行</button>
            </div>

            <div class="action-card">
              <div class="action-icon">🔧</div>
              <div class="action-content">
                <h4>系统维护</h4>
                <p>执行系统维护任务</p>
              </div>
              <button class="action-btn-small">开始</button>
            </div>

            <div class="action-card">
              <div class="action-icon">📊</div>
              <div class="action-content">
                <h4>生成报告</h4>
                <p>生成性能分析报告</p>
              </div>
              <button class="action-btn-small">生成</button>
            </div>
          </div>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'MainLayout',
  setup() {
    const router = useRouter()

    // 用户信息
    const username = ref('管理员')
    const showUserMenu = ref(false)

    // 统计数据
    const stats = reactive({
      totalGPUs: 12,
      activeGPUs: 10,
      avgUsage: 75,
      avgTemp: 68
    })

    // GPU列表数据
    const gpuList = reactive([
      { id: 'GPU-001', model: 'RTX 4090', usage: 85, temperature: 72, memory: '16GB/24GB', status: 'online', statusText: '在线' },
      { id: 'GPU-002', model: 'RTX 4080', usage: 65, temperature: 68, memory: '12GB/16GB', status: 'online', statusText: '在线' },
      { id: 'GPU-003', model: 'RTX 4070', usage: 45, temperature: 62, memory: '8GB/12GB', status: 'online', statusText: '在线' },
      { id: 'GPU-004', model: 'RTX 4060', usage: 0, temperature: 45, memory: '0GB/8GB', status: 'offline', statusText: '离线' },
      { id: 'GPU-005', model: 'RTX 4090', usage: 92, temperature: 78, memory: '20GB/24GB', status: 'warning', statusText: '警告' }
    ])

    // 切换侧边栏
    const toggleSidebar = () => {
      console.log('切换侧边栏')
    }

    // 切换用户菜单
    const toggleUserMenu = () => {
      showUserMenu.value = !showUserMenu.value
    }

    // 处理退出登录
    const handleLogout = () => {
      if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('token')
        localStorage.removeItem('rememberedUsername')
        router.push('/login')
      }
    }

    // 模拟获取数据
    const fetchData = () => {
      console.log('获取仪表盘数据')
    }

    onMounted(() => {
      fetchData()
    })

    return {
      username,
      showUserMenu,
      stats,
      gpuList,
      toggleSidebar,
      toggleUserMenu,
      handleLogout
    }
  }
}
</script>

<style scoped>
.admin-layout {
  display: flex;
  min-height: 100vh;
  background: #f8fafc;
}

/* 侧边栏样式 */
.sidebar {
  width: 260px;
  background: #1e293b;
  color: white;
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 1000;
  overflow-y: auto;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #334155;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-img {
  width: 32px;
  height: 32px;
  border-radius: 6px;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: #f1f5f9;
}

.sidebar-nav {
  padding: 20px 0;
}

.nav-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: 4px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: #cbd5e1;
  text-decoration: none;
  transition: all 0.2s ease;
  border-radius: 0;
}

.nav-link:hover {
  background: #334155;
  color: #f1f5f9;
}

.nav-item.active .nav-link {
  background: #3b82f6;
  color: white;
}

.nav-icon {
  font-size: 18px;
  width: 20px;
  text-align: center;
}

/* 主要内容区域 */
.main-wrapper {
  flex: 1;
  margin-left: 260px;
  display: flex;
  flex-direction: column;
}

/* 顶部导航栏 */
.top-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 0 24px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.menu-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #64748b;
  font-size: 14px;
}

.separator {
  color: #cbd5e1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-btn {
  position: relative;
  background: none;
  border: none;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.action-btn:hover {
  background: #f1f5f9;
}

.action-btn .icon {
  font-size: 18px;
  color: #64748b;
}

.badge {
  position: absolute;
  top: 2px;
  right: 2px;
  background: #ef4444;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}

.user-dropdown {
  position: relative;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.user-info:hover {
  background: #f1f5f9;
}

.user-avatar .avatar-img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
}

.user-role {
  font-size: 12px;
  color: #64748b;
}

.dropdown-arrow {
  font-size: 10px;
  color: #64748b;
  transition: transform 0.2s ease;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  min-width: 180px;
  padding: 8px 0;
  z-index: 1000;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  color: #374151;
  text-decoration: none;
  transition: background 0.2s ease;
}

.dropdown-item:hover {
  background: #f9fafb;
}

.item-icon {
  font-size: 16px;
  width: 16px;
}

.dropdown-divider {
  height: 1px;
  background: #e5e7eb;
  margin: 8px 0;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.dashboard-container {
  max-width: 1400px;
  margin: 0 auto;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.2s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.gpu-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.active-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.usage-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.temp-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 8px;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
}

.stat-change.positive {
  background: #dcfce7;
  color: #166534;
}

.stat-change.negative {
  background: #fef2f2;
  color: #dc2626;
}

.stat-change.neutral {
  background: #f1f5f9;
  color: #64748b;
}

/* 内容网格 */
.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

/* 图表卡片 */
.chart-card, .table-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.card-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.card-actions {
  display: flex;
  gap: 12px;
}

.time-selector {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

.refresh-btn {
  padding: 6px 12px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background: #f1f5f9;
}

/* 图表容器 */
.chart-container {
  padding: 24px;
  height: 300px;
}

.chart-placeholder {
  position: relative;
  width: 100%;
  height: 100%;
  background: #f8fafc;
  border-radius: 8px;
  overflow: hidden;
}

.chart-line {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: #e2e8f0;
}

.chart-data {
  position: relative;
  width: 100%;
  height: 100%;
}

.data-point {
  position: absolute;
  bottom: 0;
  width: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px 4px 0 0;
  transition: all 0.3s ease;
}

.data-point:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: scaleY(1.1);
}

/* 表格样式 */
.table-container {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: #f8fafc;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  border-bottom: 1px solid #e5e7eb;
}

.data-table td {
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  font-size: 14px;
  color: #374151;
}

.data-table tr:hover {
  background: #f9fafb;
}

.usage-bar {
  position: relative;
  width: 100px;
  height: 20px;
  background: #f3f4f6;
  border-radius: 10px;
  overflow: hidden;
}

.usage-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  border-radius: 10px;
  transition: width 0.3s ease;
}

.usage-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  font-weight: 500;
  color: #374151;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.online {
  background: #dcfce7;
  color: #166534;
}

.status-badge.offline {
  background: #fef2f2;
  color: #dc2626;
}

.status-badge.warning {
  background: #fef3c7;
  color: #d97706;
}

/* 快速操作 */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.action-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.2s ease;
}

.action-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.action-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.action-content {
  flex: 1;
}

.action-content h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.action-content p {
  margin: 0;
  font-size: 14px;
  color: #64748b;
}

.action-btn-small {
  padding: 8px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.action-btn-small:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .main-wrapper {
    margin-left: 0;
  }

  .menu-toggle {
    display: block;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions {
    grid-template-columns: 1fr;
  }

  .header-left {
    gap: 12px;
  }

  .header-right {
    gap: 12px;
  }

  .user-details {
    display: none;
  }

  .main-content {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .top-header {
    padding: 0 16px;
  }

  .breadcrumb {
    display: none;
  }

  .header-actions {
    gap: 8px;
  }

  .action-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .table-container {
    font-size: 12px;
  }

  .data-table th,
  .data-table td {
    padding: 8px 12px;
  }
}

/* 滚动条样式 */
.sidebar::-webkit-scrollbar {
  width: 4px;
}

.sidebar::-webkit-scrollbar-track {
  background: #1e293b;
}

.sidebar::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 2px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}
</style>
