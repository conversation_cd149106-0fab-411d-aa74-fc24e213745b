<template>
  <div class="admin-layout">
    <!-- 侧边栏 -->
    <aside
      class="sidebar"
      :class="{ 'collapsed': sidebarCollapsed }"
    >
      <div class="sidebar-header">
        <div class="logo">
          <div class="logo-icon">
            🖥️
          </div>
          <span
            v-show="!sidebarCollapsed"
            class="logo-text"
          >GPU后台管理系统</span>
        </div>
      </div>

      <nav class="sidebar-nav">
        <ul class="nav-menu">
          <li
            class="nav-item"
            :class="{ active: activeMenu === 'dashboard' }"
          >
            <a
              href="#"
              class="nav-link"
              @click="setActiveMenu('dashboard')"
            >
              <i class="nav-icon">📊</i>
              <span v-show="!sidebarCollapsed">仪表盘</span>
            </a>
          </li>
          <li
            class="nav-item"
            :class="{ active: activeMenu === 'gpu' }"
          >
            <a
              href="#"
              class="nav-link"
              @click="setActiveMenu('gpu')"
            >
              <i class="nav-icon">🖥️</i>
              <span v-show="!sidebarCollapsed">GPU管理</span>
            </a>
          </li>
          <li
            class="nav-item"
            :class="{ active: activeMenu === 'monitor' }"
          >
            <a
              href="#"
              class="nav-link"
              @click="setActiveMenu('monitor')"
            >
              <i class="nav-icon">📈</i>
              <span v-show="!sidebarCollapsed">性能监控</span>
            </a>
          </li>
          <li
            class="nav-item"
            :class="{ active: activeMenu === 'users' }"
          >
            <a
              href="#"
              class="nav-link"
              @click="setActiveMenu('users')"
            >
              <i class="nav-icon">👥</i>
              <span v-show="!sidebarCollapsed">用户管理</span>
            </a>
          </li>
          <li
            class="nav-item"
            :class="{ active: activeMenu === 'settings' }"
          >
            <a
              href="#"
              class="nav-link"
              @click="setActiveMenu('settings')"
            >
              <i class="nav-icon">⚙️</i>
              <span v-show="!sidebarCollapsed">系统设置</span>
            </a>
          </li>
          <li
            class="nav-item"
            :class="{ active: activeMenu === 'logs' }"
          >
            <a
              href="#"
              class="nav-link"
              @click="setActiveMenu('logs')"
            >
              <i class="nav-icon">📝</i>
              <span v-show="!sidebarCollapsed">日志管理</span>
            </a>
          </li>
        </ul>
      </nav>
    </aside>

    <!-- 主要内容区域 -->
    <div
      class="main-wrapper"
      :class="{ 'collapsed': sidebarCollapsed }"
    >
      <!-- 顶部导航栏 -->
      <header class="top-header">
        <div class="header-left">
          <button
            class="menu-toggle"
            @click="toggleSidebar"
          >
            <span class="hamburger-line" />
            <span class="hamburger-line" />
            <span class="hamburger-line" />
          </button>
          <div class="breadcrumb">
            <span class="breadcrumb-item">首页</span>
            <span class="breadcrumb-separator">/</span>
            <span class="breadcrumb-item active">仪表盘</span>
          </div>
        </div>

        <div class="header-right">
          <div class="search-box">
            <input
              type="text"
              placeholder="搜索..."
              class="search-input"
            >
            <i class="search-icon">🔍</i>
          </div>

          <div class="header-actions">
            <button class="action-btn notification-btn">
              <i class="icon">🔔</i>
              <span class="badge">5</span>
            </button>
            <button class="action-btn message-btn">
              <i class="icon">💬</i>
              <span class="badge">2</span>
            </button>
          </div>

          <div
            class="user-dropdown"
            @click="toggleUserMenu"
          >
            <div class="user-info">
              <div class="user-avatar">
                <img
                  src="/favicon.ico"
                  alt="Avatar"
                  class="avatar-img"
                >
              </div>
              <div class="user-details">
                <span class="user-name">{{ username }}</span>
                <span class="user-role">系统管理员</span>
              </div>
              <i class="dropdown-arrow">▼</i>
            </div>

            <div
              v-show="showUserMenu"
              class="dropdown-menu"
            >
              <a
                href="#"
                class="dropdown-item"
              >
                <i class="item-icon">👤</i>
                个人资料
              </a>
              <a
                href="#"
                class="dropdown-item"
              >
                <i class="item-icon">⚙️</i>
                账户设置
              </a>
              <div class="dropdown-divider" />
              <a
                href="#"
                class="dropdown-item"
                @click="handleLogout"
              >
                <i class="item-icon">🚪</i>
                退出登录
              </a>
            </div>
          </div>
        </div>
      </header>

      <!-- 主要内容 -->
      <main class="main-content">
        <div class="dashboard-container">
          <!-- 欢迎信息 -->
          <div class="welcome-section">
            <h1 class="page-title">
              仪表盘
            </h1>
            <p class="page-subtitle">
              欢迎回来，{{ username }}！这里是您的GPU管理概览。
            </p>
          </div>

          <!-- 统计卡片 -->
          <div class="stats-grid">
            <div class="stat-card primary">
              <div class="stat-icon">
                <i class="icon">🖥️</i>
              </div>
              <div class="stat-content">
                <div class="stat-number">
                  {{ stats.totalGPUs }}
                </div>
                <div class="stat-label">
                  总GPU数量
                </div>
                <div class="stat-trend positive">
                  <i class="trend-icon">📈</i>
                  <span>+12%</span>
                </div>
              </div>
            </div>

            <div class="stat-card success">
              <div class="stat-icon">
                <i class="icon">⚡</i>
              </div>
              <div class="stat-content">
                <div class="stat-number">
                  {{ stats.activeGPUs }}
                </div>
                <div class="stat-label">
                  活跃GPU
                </div>
                <div class="stat-trend positive">
                  <i class="trend-icon">📈</i>
                  <span>+8%</span>
                </div>
              </div>
            </div>

            <div class="stat-card warning">
              <div class="stat-icon">
                <i class="icon">📊</i>
              </div>
              <div class="stat-content">
                <div class="stat-number">
                  {{ stats.avgUsage }}%
                </div>
                <div class="stat-label">
                  平均使用率
                </div>
                <div class="stat-trend negative">
                  <i class="trend-icon">📉</i>
                  <span>-3%</span>
                </div>
              </div>
            </div>

            <div class="stat-card info">
              <div class="stat-icon">
                <i class="icon">🌡️</i>
              </div>
              <div class="stat-content">
                <div class="stat-number">
                  {{ stats.avgTemp }}°C
                </div>
                <div class="stat-label">
                  平均温度
                </div>
                <div class="stat-trend neutral">
                  <i class="trend-icon">➖</i>
                  <span>0%</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 图表和表格区域 -->
          <div class="content-grid">
            <!-- GPU使用率图表 -->
            <div class="chart-card">
              <div class="card-header">
                <h3 class="card-title">
                  GPU使用率趋势
                </h3>
                <div class="card-actions">
                  <select class="time-selector">
                    <option>最近24小时</option>
                    <option>最近7天</option>
                    <option>最近30天</option>
                  </select>
                  <button class="refresh-btn">
                    🔄
                  </button>
                </div>
              </div>
              <div class="chart-container">
                <div class="chart-placeholder">
                  <div class="chart-grid">
                    <div
                      v-for="i in 5"
                      :key="i"
                      class="grid-line"
                      :style="{ bottom: (i * 20) + '%' }"
                    />
                  </div>
                  <div class="chart-data">
                    <div
                      v-for="(point, index) in chartData"
                      :key="index"
                      class="data-point"
                      :style="{ left: (index * 10) + '%', height: point + '%' }"
                    />
                  </div>
                  <div class="chart-labels">
                    <span>00:00</span>
                    <span>06:00</span>
                    <span>12:00</span>
                    <span>18:00</span>
                    <span>24:00</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 系统状态 -->
            <div class="status-card">
              <div class="card-header">
                <h3 class="card-title">
                  系统状态
                </h3>
                <span class="status-indicator online">在线</span>
              </div>
              <div class="status-content">
                <div class="status-item">
                  <div class="status-label">
                    CPU使用率
                  </div>
                  <div class="status-progress">
                    <div
                      class="progress-bar"
                      :style="{ width: systemStatus.cpuUsage + '%' }"
                    />
                  </div>
                  <div class="status-value">
                    {{ systemStatus.cpuUsage }}%
                  </div>
                </div>
                <div class="status-item">
                  <div class="status-label">
                    内存使用率
                  </div>
                  <div class="status-progress">
                    <div
                      class="progress-bar"
                      :style="{ width: systemStatus.memoryUsage + '%' }"
                    />
                  </div>
                  <div class="status-value">
                    {{ systemStatus.memoryUsage }}%
                  </div>
                </div>
                <div class="status-item">
                  <div class="status-label">
                    磁盘使用率
                  </div>
                  <div class="status-progress">
                    <div
                      class="progress-bar"
                      :style="{ width: systemStatus.diskUsage + '%' }"
                    />
                  </div>
                  <div class="status-value">
                    {{ systemStatus.diskUsage }}%
                  </div>
                </div>
                <div class="status-item">
                  <div class="status-label">
                    网络流量
                  </div>
                  <div class="status-progress">
                    <div
                      class="progress-bar"
                      :style="{ width: systemStatus.networkUsage + '%' }"
                    />
                  </div>
                  <div class="status-value">
                    {{ systemStatus.networkUsage }}%
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- GPU设备列表 -->
          <div class="table-card">
            <div class="card-header">
              <h3 class="card-title">
                GPU设备列表
              </h3>
              <div class="card-actions">
                <button class="action-btn">
                  添加设备
                </button>
                <button class="refresh-btn">
                  🔄 刷新
                </button>
              </div>
            </div>
            <div class="table-container">
              <table class="data-table">
                <thead>
                  <tr>
                    <th>设备ID</th>
                    <th>设备名称</th>
                    <th>型号</th>
                    <th>使用率</th>
                    <th>温度</th>
                    <th>内存使用</th>
                    <th>状态</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="gpu in gpuList"
                    :key="gpu.id"
                    class="table-row"
                  >
                    <td class="device-id">
                      {{ gpu.id }}
                    </td>
                    <td class="device-name">
                      {{ gpu.name }}
                    </td>
                    <td class="device-model">
                      {{ gpu.model }}
                    </td>
                    <td class="device-usage">
                      <div class="usage-bar">
                        <div
                          class="usage-fill"
                          :style="{ width: gpu.usage + '%' }"
                          :class="getUsageClass(gpu.usage)"
                        />
                        <span class="usage-text">{{ gpu.usage }}%</span>
                      </div>
                    </td>
                    <td
                      class="device-temp"
                      :class="getTempClass(gpu.temperature)"
                    >
                      {{ gpu.temperature }}°C
                    </td>
                    <td class="device-memory">
                      {{ gpu.memoryUsed }}/{{ gpu.memoryTotal }}GB
                    </td>
                    <td class="device-status">
                      <span
                        class="status-badge"
                        :class="gpu.status"
                      >
                        {{ getStatusText(gpu.status) }}
                      </span>
                    </td>
                    <td class="device-actions">
                      <button
                        class="btn-small btn-primary"
                        @click="viewDetails(gpu)"
                      >
                        详情
                      </button>
                      <button
                        class="btn-small btn-warning"
                        @click="restartGPU(gpu)"
                      >
                        重启
                      </button>
                      <button
                        class="btn-small btn-danger"
                        @click="stopGPU(gpu)"
                      >
                        停止
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 快速操作 -->
          <div class="quick-actions">
            <div class="action-card">
              <div class="action-icon primary">
                ⚡
              </div>
              <div class="action-content">
                <h4>性能优化</h4>
                <p>自动优化GPU性能配置</p>
              </div>
              <button class="action-btn-small">
                执行
              </button>
            </div>

            <div class="action-card">
              <div class="action-icon success">
                🔧
              </div>
              <div class="action-content">
                <h4>系统维护</h4>
                <p>执行系统清理和维护</p>
              </div>
              <button class="action-btn-small">
                开始
              </button>
            </div>

            <div class="action-card">
              <div class="action-icon info">
                📊
              </div>
              <div class="action-content">
                <h4>生成报告</h4>
                <p>生成详细的性能报告</p>
              </div>
              <button class="action-btn-small">
                生成
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'MainLayout',
  setup() {
    const router = useRouter()

    // 响应式数据
    const username = ref('管理员')
    const showUserMenu = ref(false)
    const sidebarCollapsed = ref(false)
    const activeMenu = ref('dashboard')

    // 统计数据
    const stats = reactive({
      totalGPUs: 12,
      activeGPUs: 10,
      avgUsage: 75,
      avgTemp: 68
    })

    // 系统状态数据
    const systemStatus = reactive({
      cpuUsage: 45,
      memoryUsage: 67,
      diskUsage: 82,
      networkUsage: 35
    })

    // 图表数据
    const chartData = ref([30, 45, 60, 40, 75, 55, 80, 65, 90, 70])

    // GPU设备列表
    const gpuList = reactive([
      {
        id: 'GPU-001',
        name: 'Tesla V100',
        model: 'NVIDIA Tesla V100',
        usage: 85,
        temperature: 72,
        memoryUsed: 16,
        memoryTotal: 32,
        status: 'online'
      },
      {
        id: 'GPU-002',
        name: 'RTX 4090',
        model: 'NVIDIA RTX 4090',
        usage: 65,
        temperature: 68,
        memoryUsed: 12,
        memoryTotal: 24,
        status: 'online'
      },
      {
        id: 'GPU-003',
        name: 'RTX 4080',
        model: 'NVIDIA RTX 4080',
        usage: 45,
        temperature: 62,
        memoryUsed: 8,
        memoryTotal: 16,
        status: 'online'
      },
      {
        id: 'GPU-004',
        name: 'RTX 4070',
        model: 'NVIDIA RTX 4070',
        usage: 0,
        temperature: 45,
        memoryUsed: 0,
        memoryTotal: 12,
        status: 'offline'
      },
      {
        id: 'GPU-005',
        name: 'A100',
        model: 'NVIDIA A100',
        usage: 92,
        temperature: 78,
        memoryUsed: 38,
        memoryTotal: 40,
        status: 'warning'
      }
    ])

    // 方法
    const toggleSidebar = () => {
      sidebarCollapsed.value = !sidebarCollapsed.value
    }

    const toggleUserMenu = () => {
      showUserMenu.value = !showUserMenu.value
    }

    const setActiveMenu = (menu) => {
      activeMenu.value = menu
    }

    const handleLogout = () => {
      if (confirm('确定要退出登录吗？')) {
        localStorage.removeItem('token')
        localStorage.removeItem('rememberedUsername')
        router.push('/login')
      }
    }

    const getUsageClass = (usage) => {
      if (usage >= 80) return 'high'
      if (usage >= 50) return 'medium'
      return 'low'
    }

    const getTempClass = (temp) => {
      if (temp >= 75) return 'temp-high'
      if (temp >= 65) return 'temp-medium'
      return 'temp-normal'
    }

    const getStatusText = (status) => {
      const statusMap = {
        online: '在线',
        offline: '离线',
        warning: '警告'
      }
      return statusMap[status] || '未知'
    }

    const viewDetails = (gpu) => {
      console.log('查看GPU详情:', gpu)
    }

    const restartGPU = (gpu) => {
      if (confirm(`确定要重启 ${gpu.name} 吗？`)) {
        console.log('重启GPU:', gpu)
      }
    }

    const stopGPU = (gpu) => {
      if (confirm(`确定要停止 ${gpu.name} 吗？`)) {
        console.log('停止GPU:', gpu)
      }
    }

    // 模拟数据更新
    const updateData = () => {
      // 更新图表数据
      chartData.value = chartData.value.map(() => Math.floor(Math.random() * 100))

      // 更新系统状态
      systemStatus.cpuUsage = Math.floor(Math.random() * 100)
      systemStatus.memoryUsage = Math.floor(Math.random() * 100)
      systemStatus.diskUsage = Math.floor(Math.random() * 100)
      systemStatus.networkUsage = Math.floor(Math.random() * 100)
    }

    onMounted(() => {
      // 定期更新数据
      setInterval(updateData, 5000)
    })

    return {
      username,
      showUserMenu,
      sidebarCollapsed,
      activeMenu,
      stats,
      systemStatus,
      chartData,
      gpuList,
      toggleSidebar,
      toggleUserMenu,
      setActiveMenu,
      handleLogout,
      getUsageClass,
      getTempClass,
      getStatusText,
      viewDetails,
      restartGPU,
      stopGPU
    }
  }
}
</script>

<style scoped>
/* 全局布局 */
.admin-layout {
  display: flex;
  min-height: 100vh;
  background: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 侧边栏样式 */
.sidebar {
  width: 280px;
  background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
  color: white;
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 1000;
  overflow-y: auto;
  transition: all 0.3s ease;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar-header {
  padding: 24px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.logo-text {
  font-size: 18px;
  font-weight: 700;
  color: #f1f5f9;
  white-space: nowrap;
  transition: opacity 0.3s ease;
}

.sidebar.collapsed .logo-text {
  opacity: 0;
}

.sidebar-nav {
  padding: 20px 0;
}

.nav-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.nav-item {
  margin-bottom: 4px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 14px 20px;
  color: #cbd5e1;
  text-decoration: none;
  transition: all 0.3s ease;
  border-radius: 0;
  position: relative;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #f1f5f9;
  transform: translateX(4px);
}

.nav-item.active .nav-link {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.nav-item.active .nav-link::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: #60a5fa;
  border-radius: 0 4px 4px 0;
}

.nav-icon {
  font-size: 18px;
  width: 20px;
  text-align: center;
  transition: transform 0.3s ease;
}

.nav-link:hover .nav-icon {
  transform: scale(1.1);
}

.sidebar.collapsed .nav-link span {
  opacity: 0;
  width: 0;
}

/* 主要内容区域 */
.main-wrapper {
  flex: 1;
  margin-left: 280px;
  display: flex;
  flex-direction: column;
  transition: margin-left 0.3s ease;
}

.main-wrapper.collapsed {
  margin-left: 80px;
}

/* 顶部导航栏 */
.top-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 0 24px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.menu-toggle {
  display: flex;
  flex-direction: column;
  gap: 4px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background 0.2s ease;
}

.menu-toggle:hover {
  background: #f1f5f9;
}

.hamburger-line {
  width: 20px;
  height: 2px;
  background: #64748b;
  border-radius: 1px;
  transition: all 0.3s ease;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #64748b;
  font-size: 14px;
}

.breadcrumb-item.active {
  color: #1e293b;
  font-weight: 500;
}

.breadcrumb-separator {
  color: #cbd5e1;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 300px;
  padding: 10px 40px 10px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  background: #f8fafc;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  background: white;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-icon {
  position: absolute;
  right: 12px;
  color: #64748b;
  font-size: 16px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-btn {
  position: relative;
  background: none;
  border: none;
  padding: 10px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f1f5f9;
  transform: translateY(-1px);
}

.action-btn .icon {
  font-size: 18px;
  color: #64748b;
}

.badge {
  position: absolute;
  top: 6px;
  right: 6px;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

.user-dropdown {
  position: relative;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.user-info:hover {
  background: #f8fafc;
  border-color: #e2e8f0;
}

.user-avatar .avatar-img {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 2px solid #e2e8f0;
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

.user-role {
  font-size: 12px;
  color: #64748b;
}

.dropdown-arrow {
  font-size: 10px;
  color: #64748b;
  transition: transform 0.2s ease;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  min-width: 200px;
  padding: 8px 0;
  z-index: 1000;
  margin-top: 8px;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  color: #374151;
  text-decoration: none;
  transition: background 0.2s ease;
  font-size: 14px;
}

.dropdown-item:hover {
  background: #f9fafb;
}

.item-icon {
  font-size: 16px;
  width: 16px;
}

.dropdown-divider {
  height: 1px;
  background: #e5e7eb;
  margin: 8px 0;
}

/* 主要内容 */
.main-content {
  flex: 1;
  padding: 32px;
  overflow-y: auto;
}

.dashboard-container {
  max-width: 1400px;
  margin: 0 auto;
}

.welcome-section {
  margin-bottom: 32px;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

/* 统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.stat-card.primary::before {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.stat-card.success::before {
  background: linear-gradient(90deg, #10b981, #059669);
}

.stat-card.warning::before {
  background: linear-gradient(90deg, #f59e0b, #d97706);
}

.stat-card.info::before {
  background: linear-gradient(90deg, #06b6d4, #0891b2);
}

.stat-card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-4px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 36px;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 4px;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 8px;
  display: inline-flex;
}

.stat-trend.positive {
  background: #dcfce7;
  color: #166534;
}

.stat-trend.negative {
  background: #fef2f2;
  color: #dc2626;
}

.stat-trend.neutral {
  background: #f1f5f9;
  color: #64748b;
}

.trend-icon {
  font-size: 10px;
}

/* 内容网格 */
.content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

/* 卡片样式 */
.chart-card, .status-card, .table-card {
  background: white;
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.card-header {
  padding: 24px 24px 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.card-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.time-selector {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

.refresh-btn {
  padding: 8px 12px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.action-btn {
  padding: 8px 16px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 图表容器 */
.chart-container {
  padding: 0 24px 24px 24px;
  height: 300px;
}

.chart-placeholder {
  position: relative;
  width: 100%;
  height: 100%;
  background: #f8fafc;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid #e2e8f0;
}

.chart-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.grid-line {
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  background: #e2e8f0;
}

.chart-data {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 20px;
}

.data-point {
  position: absolute;
  bottom: 20px;
  width: 8px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 4px 4px 0 0;
  transition: all 0.3s ease;
}

.data-point:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: scaleY(1.1);
}

.chart-labels {
  position: absolute;
  bottom: 0;
  left: 20px;
  right: 20px;
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #64748b;
  padding: 8px 0;
}

/* 状态卡片 */
.status-indicator {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.status-indicator.online {
  background: #dcfce7;
  color: #166534;
}

.status-content {
  padding: 0 24px 24px 24px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-label {
  min-width: 80px;
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.status-progress {
  flex: 1;
  height: 8px;
  background: #f1f5f9;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.status-value {
  min-width: 40px;
  text-align: right;
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

/* 表格样式 */
.table-container {
  overflow-x: auto;
  padding: 0 24px 24px 24px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: #f8fafc;
  padding: 16px 12px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
  border-bottom: 2px solid #e5e7eb;
  white-space: nowrap;
}

.data-table td {
  padding: 16px 12px;
  border-bottom: 1px solid #f3f4f6;
  font-size: 14px;
  color: #374151;
  white-space: nowrap;
}

.table-row:hover {
  background: #f9fafb;
}

.device-id {
  font-family: 'Monaco', 'Menlo', monospace;
  font-weight: 600;
  color: #3b82f6;
}

.device-name {
  font-weight: 600;
  color: #1e293b;
}

.device-model {
  color: #64748b;
}

.usage-bar {
  position: relative;
  width: 120px;
  height: 24px;
  background: #f3f4f6;
  border-radius: 12px;
  overflow: hidden;
}

.usage-fill {
  height: 100%;
  border-radius: 12px;
  transition: width 0.3s ease;
}

.usage-fill.low {
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
}

.usage-fill.medium {
  background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
}

.usage-fill.high {
  background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
}

.usage-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  font-weight: 600;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.device-temp {
  font-weight: 600;
}

.device-temp.temp-normal {
  color: #10b981;
}

.device-temp.temp-medium {
  color: #f59e0b;
}

.device-temp.temp-high {
  color: #ef4444;
}

.device-memory {
  font-family: 'Monaco', 'Menlo', monospace;
  color: #64748b;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.online {
  background: #dcfce7;
  color: #166534;
}

.status-badge.offline {
  background: #fef2f2;
  color: #dc2626;
}

.status-badge.warning {
  background: #fef3c7;
  color: #d97706;
}

.device-actions {
  display: flex;
  gap: 8px;
}

.btn-small {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.btn-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.btn-warning:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3);
}

.btn-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.btn-danger:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
}

/* 快速操作 */
.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
}

.action-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.action-card:hover {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-2px);
  border-color: #cbd5e1;
}

.action-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-icon.primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.action-icon.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.action-icon.info {
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
}

.action-content {
  flex: 1;
}

.action-content h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.action-content p {
  margin: 0;
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
}

.action-btn-small {
  padding: 10px 20px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.action-btn-small:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .main-wrapper {
    margin-left: 0;
  }

  .main-wrapper.collapsed {
    margin-left: 0;
  }

  .menu-toggle {
    display: flex;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .quick-actions {
    grid-template-columns: 1fr;
  }

  .search-box {
    display: none;
  }

  .header-left {
    gap: 16px;
  }

  .header-right {
    gap: 12px;
  }

  .user-details {
    display: none;
  }

  .main-content {
    padding: 20px 16px;
  }

  .top-header {
    padding: 0 16px;
  }

  .breadcrumb {
    display: none;
  }
}

@media (max-width: 480px) {
  .header-actions {
    gap: 8px;
  }

  .action-card {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .table-container {
    font-size: 12px;
  }

  .data-table th,
  .data-table td {
    padding: 12px 8px;
  }

  .usage-bar {
    width: 80px;
    height: 20px;
  }

  .device-actions {
    flex-direction: column;
    gap: 4px;
  }

  .btn-small {
    padding: 4px 8px;
    font-size: 11px;
  }
}

/* 滚动条样式 */
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

.main-content::-webkit-scrollbar {
  width: 6px;
}

.main-content::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.main-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-card,
.chart-card,
.status-card,
.table-card,
.action-card {
  animation: fadeIn 0.6s ease-out;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

/* 加载状态 */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #e2e8f0;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>