<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>
    
    <!-- 登录表单容器 -->
    <div class="login-wrapper">
      <div class="login-box">
        <!-- Logo和标题 -->
        <div class="login-header">
          <div class="logo">
            <img src="/favicon.ico" alt="Logo" class="logo-img">
          </div>
          <h1 class="title">GPU管理后台</h1>
<parameter name="subtitle">管理员登录</p>
        </div>

        <!-- 登录表单 -->
        <div class="login-form">
          <form @submit.prevent="handleLogin">
            <div class="form-group">
              <div class="input-wrapper">
                <i class="icon user-icon">👤</i>
                <input
                  v-model="loginForm.username"
                  type="text"
                  placeholder="请输入用户名"
                  class="form-input"
                  required
                >
              </div>
            </div>

            <div class="form-group">
              <div class="input-wrapper">
                <i class="icon lock-icon">🔒</i>
                <input
                  v-model="loginForm.password"
                  :type="showPassword ? 'text' : 'password'"
                  placeholder="请输入密码"
                  class="form-input"
                  required
                >
                <i 
                  class="icon eye-icon"
                  @click="togglePassword"
                >
                  {{ showPassword ? '👁️' : '👁️‍🗨️' }}
                </i>
              </div>
            </div>

            <div class="form-options">
              <label class="remember-me">
                <input type="checkbox" v-model="rememberMe">
                <span class="checkmark"></span>
                记住密码
              </label>
              <a href="#" class="forgot-password">忘记密码？</a>
            </div>

            <button 
              type="submit" 
              class="login-btn"
              :disabled="loading"
            >
              <span v-if="loading" class="loading-spinner"></span>
              {{ loading ? '登录中...' : '登录' }}
            </button>
          </form>
        </div>

        <!-- 其他登录方式 -->
        <div class="other-login">
          <div class="divider">
            <span>或</span>
          </div>
          <div class="social-login">
            <button class="social-btn wechat">
              <i class="social-icon">💬</i>
              微信登录
            </button>
            <button class="social-btn qq">
              <i class="social-icon">🐧</i>
              QQ登录
            </button>
          </div>
        </div>

        <!-- 注册链接 -->
        <div class="register-link">
          还没有账号？<a href="#" @click="showRegister = true">立即注册</a>
        </div>
      </div>
    </div>

    <!-- 版权信息 -->
    <div class="footer">
      <p>&copy; 2024 GPU管理系统. All rights reserved.</p>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import request from '@/utils/request'

export default {
  name: 'LoginRegister',
  setup() {
    const router = useRouter()
    
    // 响应式数据
    const loginForm = reactive({
      username: '',
      password: ''
    })
    
    const showPassword = ref(false)
    const rememberMe = ref(false)
    const loading = ref(false)
    const showRegister = ref(false)

    // 切换密码显示
    const togglePassword = () => {
      showPassword.value = !showPassword.value
    }

    // 处理登录
    const handleLogin = async () => {
      if (!loginForm.username || !loginForm.password) {
        alert('请输入用户名和密码')
        return
      }

      loading.value = true
      
      try {
        const response = await request.post('/api/login', {
          username: loginForm.username,
          password: loginForm.password
        })
        
        if (response.data.success) {
          // 保存token
          localStorage.setItem('token', response.data.token)
          
          // 如果选择记住密码，保存到localStorage
          if (rememberMe.value) {
            localStorage.setItem('rememberedUsername', loginForm.username)
          }
          
          alert('登录成功！')
          router.push('/')
        } else {
          alert(response.data.message || '登录失败')
        }
      } catch (error) {
        console.error('登录错误:', error)
        alert('登录失败，请检查网络连接')
      } finally {
        loading.value = false
      }
    }

    // 页面加载时检查是否有记住的用户名
    const rememberedUsername = localStorage.getItem('rememberedUsername')
    if (rememberedUsername) {
      loginForm.username = rememberedUsername
      rememberMe.value = true
    }

    return {
      loginForm,
      showPassword,
      rememberMe,
      loading,
      showRegister,
      togglePassword,
      handleLogin
    }
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* 登录框 */
.login-wrapper {
  z-index: 1;
}

.login-box {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  width: 400px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 头部 */
.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.logo {
  margin-bottom: 20px;
}

.logo-img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
}

.title {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin: 0 0 10px 0;
}

.subtitle {
  color: #666;
  font-size: 16px;
  margin: 0;
}

/* 表单 */
.form-group {
  margin-bottom: 20px;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  width: 100%;
  padding: 15px 50px 15px 50px;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: #fff;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.icon {
  position: absolute;
  font-size: 18px;
  color: #999;
  z-index: 1;
}

.user-icon, .lock-icon {
  left: 18px;
}

.eye-icon {
  right: 18px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.eye-icon:hover {
  color: #667eea;
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.remember-me {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #666;
}

.remember-me input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid #ddd;
  border-radius: 4px;
  margin-right: 8px;
  position: relative;
  transition: all 0.3s ease;
}

.remember-me input[type="checkbox"]:checked + .checkmark {
  background: #667eea;
  border-color: #667eea;
}

.remember-me input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  color: white;
  font-size: 12px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.forgot-password {
  color: #667eea;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: #5a6fd8;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.login-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.login-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 其他登录方式 */
.other-login {
  margin: 30px 0;
}

.divider {
  text-align: center;
  position: relative;
  margin: 20px 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e1e5e9;
}

.divider span {
  background: rgba(255, 255, 255, 0.95);
  padding: 0 15px;
  color: #999;
  font-size: 14px;
}

.social-login {
  display: flex;
  gap: 15px;
}

.social-btn {
  flex: 1;
  padding: 12px;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
}

.social-btn:hover {
  border-color: #667eea;
  color: #667eea;
}

.social-icon {
  font-size: 16px;
}

/* 注册链接 */
.register-link {
  text-align: center;
  margin-top: 20px;
  font-size: 14px;
  color: #666;
}

.register-link a {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
}

.register-link a:hover {
  color: #5a6fd8;
}

/* 版权信息 */
.footer {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-box {
    width: 90%;
    padding: 30px 20px;
  }
  
  .title {
    font-size: 24px;
  }
  
  .social-login {
    flex-direction: column;
  }
}
</style>
